@allure.feature:视频播放功能
Feature: Allure 报告演示 - 视频播放测试

  Background: 测试环境准备
    Given 设置测试环境

  @allure.story:基础播放功能
  @allure.severity:critical
  @smoke
  Scenario: 用户播放视频 - 成功场景
    Given 用户已登录应用
    And 用户在首页
    When 用户点击视频播放按钮
    And 等待视频加载完成
    Then 视频应该开始播放
    And 播放器显示正常

  @allure.story:播放控制功能  
  @allure.severity:normal
  @regression
  Scenario: 用户暂停和恢复视频
    Given 用户已登录应用
    And 视频正在播放
    When 用户点击暂停按钮
    Then 视频应该暂停
    When 用户点击播放按钮
    Then 视频应该恢复播放

  @allure.story:播放质量设置
  @allure.severity:minor
  @functional
  Scenario Outline: 用户切换视频清晰度
    Given 用户已登录应用
    And 视频正在播放
    When 用户选择 "<quality>" 清晰度
    Then 视频清晰度应该切换到 "<quality>"
    And 播放应该继续

    Examples:
      | quality |
      | 480P    |
      | 720P    |
      | 1080P   |

  @allure.story:错误处理
  @allure.severity:major
  @negative
  Scenario: 网络异常时的播放行为
    Given 用户已登录应用
    And 用户在视频播放页面
    When 网络连接中断
    And 用户尝试播放视频
    Then 应该显示网络错误提示
    And 提供重试选项
