# Behave-Allure 测试报告完整教程

## 📋 目录
1. [环境准备](#1-环境准备)
2. [项目配置](#2-项目配置)
3. [编写测试用例](#3-编写测试用例)
4. [运行测试](#4-运行测试)
5. [Allure 注解详解](#5-allure-注解详解)
6. [高级功能](#6-高级功能)
7. [最佳实践](#7-最佳实践)

## 1. 环境准备

### 1.1 安装依赖

```bash
# 安装 allure-behave 格式化器
pip install allure-behave

# 安装 allure 命令行工具 (macOS)
brew install allure

# 或者下载二进制文件 (Windows/Linux)
# https://github.com/allure-framework/allure2/releases
```

### 1.2 验证安装
```bash
# 检查 allure 版本
allure --version

# 检查 Python 包
python -c "import allure_behave; print('allure-behave 安装成功')"
```

## 2. 项目配置

### 2.1 behave.ini 配置
```ini
[behave]
format=allure_behave.formatter:AllureFormatter
outfiles=allure-results
log_capture=false
show_timings=true
```

### 2.2 目录结构
```
project/
├── features/
│   ├── allure_demo.feature
│   └── environment.py
├── steps/
│   └── allure_demo_steps.py
├── allure-results/
│   └── environment.properties
├── allure-report/
├── behave.ini
└── requirements.txt
```

## 3. 编写测试用例

### 3.1 Feature 文件标签使用

在 Feature 文件中使用 Allure 标签：

```gherkin
@allure.feature:视频播放功能
Feature: Allure 报告演示

  @allure.story:基础播放功能
  @allure.severity:critical
  @smoke
  Scenario: 用户播放视频
    Given 用户已登录应用
    When 用户点击播放按钮
    Then 视频应该开始播放
```

### 3.2 Steps 文件中的 Allure 使用

```python
import allure
from behave import given, when, then

@allure.step("用户登录应用")
@given('用户已登录应用')
def user_login(context):
    with allure.step("输入用户凭据"):
        # 测试逻辑
        pass

    allure.attach(
        "登录成功",
        name="登录状态",
        attachment_type=allure.attachment_type.TEXT
    )
```

## 4. 运行测试

### 4.1 基本运行命令

```bash
# 运行所有测试
behave

# 运行特定 feature
behave features/allure_demo.feature

# 按标签运行
behave --tags=@smoke

# 指定平台
PLATFORM=android behave features/allure_demo.feature
```

### 4.2 使用提供的运行脚本

```bash
# 使用演示脚本
python tutorial/run_allure_demo.py

# 指定参数
python tutorial/run_allure_demo.py -f allure_demo.feature -p android --serve
```

### 4.3 生成和查看报告

```bash
# 生成报告
allure generate allure-results -o allure-report --clean

# 启动报告服务器
allure serve allure-results

# 或者直接打开生成的报告
open allure-report/index.html
```

## 5. Allure 注解详解

### 5.1 基本注解

#### @allure.feature() - 功能模块
```python
@allure.feature("用户管理")
def test_user_management():
    pass
```

#### @allure.story() - 用户故事
```python
@allure.story("用户登录")
def test_user_login():
    pass
```

#### @allure.severity() - 严重程度
```python
@allure.severity(allure.severity_level.CRITICAL)  # 严重
@allure.severity(allure.severity_level.MAJOR)     # 重要
@allure.severity(allure.severity_level.NORMAL)    # 普通
@allure.severity(allure.severity_level.MINOR)     # 次要
@allure.severity(allure.severity_level.TRIVIAL)   # 轻微
```

#### @allure.title() - 测试标题
```python
@allure.title("用户登录功能测试")
def test_login():
    pass
```

### 5.2 动态注解

```python
# 在运行时动态设置
allure.dynamic.feature("动态功能名称")
allure.dynamic.story("动态故事名称")
allure.dynamic.title("动态标题")
allure.dynamic.description("动态描述")
allure.dynamic.tag("动态标签")
allure.dynamic.severity(allure.severity_level.CRITICAL)
```

### 5.3 步骤和附件

#### @allure.step() - 测试步骤
```python
@allure.step("执行登录操作")
def perform_login(username, password):
    with allure.step(f"输入用户名: {username}"):
        # 输入用户名逻辑
        pass

    with allure.step("输入密码"):
        # 输入密码逻辑（不记录敏感信息）
        pass
```

#### allure.attach() - 添加附件
```python
# 文本附件
allure.attach(
    "这是测试数据",
    name="测试数据",
    attachment_type=allure.attachment_type.TEXT
)

# JSON 附件
import json
allure.attach(
    json.dumps({"key": "value"}, indent=2),
    name="JSON 数据",
    attachment_type=allure.attachment_type.JSON
)

# 图片附件
allure.attach.file(
    "screenshot.png",
    name="截图",
    attachment_type=allure.attachment_type.PNG
)
```

### 5.4 链接注解

```python
@allure.link("https://example.com", name="相关链接")
@allure.issue("BUG-123", name="相关缺陷")
@allure.testcase("TC-456", name="测试用例")
def test_with_links():
    pass
```

## 6. 高级功能

### 6.1 环境信息配置

创建 `allure-results/environment.properties`：
```properties
Browser=Chrome
Browser.Version=91.0.4472.124
Platform=Android
OS=macOS
Python.Version=3.9.7
App.Version=1.0.0
```

### 6.2 分类和标签

#### 在 Feature 文件中使用标签
```gherkin
@smoke @regression @api
Scenario: API 测试场景
```

#### 在 Steps 中动态添加标签
```python
@given('设置测试环境')
def setup_environment(context):
    allure.dynamic.tag("环境设置")
    allure.dynamic.tag(f"平台-{context.platform}")
```

### 6.3 失败重试和截图

```python
def after_step(context, step):
    if step.status == "failed":
        # 添加失败截图
        screenshot_path = take_screenshot()
        if screenshot_path:
            allure.attach.file(
                screenshot_path,
                name="失败截图",
                attachment_type=allure.attachment_type.PNG
            )

        # 添加失败信息
        allure.attach(
            f"步骤失败: {step.name}\n错误: {step.exception}",
            name="失败详情",
            attachment_type=allure.attachment_type.TEXT
        )
```

### 6.4 参数化测试报告

```python
@allure.step("测试不同清晰度: {quality}")
def test_video_quality(quality):
    allure.dynamic.title(f"视频清晰度测试 - {quality}")
    allure.dynamic.description(f"测试 {quality} 清晰度的播放效果")

## 7. 最佳实践

### 7.1 测试组织结构

```python
# 推荐的测试组织方式
@allure.epic("移动应用测试")           # 史诗级别
@allure.feature("视频播放模块")        # 功能模块
@allure.story("基础播放功能")          # 用户故事
@allure.severity(allure.severity_level.CRITICAL)
def test_basic_video_playback():
    pass
```

### 7.2 命名规范

- **Feature**: 使用业务功能名称，如 "用户管理"、"视频播放"
- **Story**: 使用用户故事格式，如 "用户登录"、"播放视频"
- **Step**: 使用动作描述，如 "点击登录按钮"、"验证播放状态"

### 7.3 附件使用建议

```python
# ✅ 好的做法
allure.attach(
    json.dumps(test_data, indent=2),
    name="测试数据",
    attachment_type=allure.attachment_type.JSON
)

# ❌ 避免的做法 - 附件过大
# allure.attach(huge_log_content, ...)
```

### 7.4 步骤粒度控制

```python
# ✅ 合适的步骤粒度
@allure.step("用户登录流程")
def user_login_process(username, password):
    with allure.step("打开登录页面"):
        open_login_page()

    with allure.step(f"输入用户名: {username}"):
        input_username(username)

    with allure.step("输入密码"):
        input_password(password)  # 不记录密码内容

    with allure.step("点击登录按钮"):
        click_login_button()

# ❌ 过于细粒度的步骤
# @allure.step("移动鼠标到按钮")
# @allure.step("点击鼠标左键")
```

### 7.5 环境配置管理

```python
# environment.py 中的最佳实践
def before_all(context):
    # 动态生成环境信息
    env_info = {
        "test_environment": os.environ.get("TEST_ENV", "dev"),
        "platform": os.environ.get("PLATFORM", "android"),
        "app_version": get_app_version(),
        "device_info": get_device_info()
    }

    # 写入环境文件
    with open("allure-results/environment.properties", "w") as f:
        for key, value in env_info.items():
            f.write(f"{key}={value}\n")
```

## 8. 实际运行示例

### 8.1 运行演示测试

```bash
# 1. 进入项目目录
cd /path/to/your/project

# 2. 运行演示测试
python tutorial/run_allure_demo.py -f allure_demo.feature -p android

# 3. 查看生成的报告
open allure-report/index.html
```

### 8.2 集成到 CI/CD

```yaml
# GitHub Actions 示例
name: Allure Report
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9

      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install allure-behave

      - name: Run tests
        run: |
          behave --format=allure_behave.formatter:AllureFormatter --outdir=allure-results

      - name: Generate Allure Report
        uses: simple-elf/allure-report-action@master
        if: always()
        with:
          allure_results: allure-results
          allure_history: allure-history
```

### 8.3 报告分析要点

1. **Overview**: 查看测试执行总览
2. **Categories**: 查看失败分类
3. **Suites**: 按测试套件查看结果
4. **Graphs**: 查看趋势图表
5. **Timeline**: 查看执行时间线
6. **Behaviors**: 按 BDD 行为查看

## 9. 常见问题解决

### 9.1 报告不显示中文
```python
# 在 steps 文件开头添加
# -*- coding: utf-8 -*-
import sys
sys.stdout.reconfigure(encoding='utf-8')
```

### 9.2 附件过大导致报告加载慢
```python
# 限制附件大小
def safe_attach(content, name, attachment_type):
    if len(content) > 1024 * 1024:  # 1MB
        content = content[:1024*1024] + "\n... (内容被截断)"
    allure.attach(content, name, attachment_type)
```

### 9.3 步骤嵌套过深
```python
# 使用 allure.step 的 context manager 形式
with allure.step("主要操作"):
    # 避免在这里再嵌套太多 allure.step
    perform_main_action()
```

## 10. 总结

通过本教程，你应该能够：

1. ✅ 配置 behave-allure 环境
2. ✅ 编写带有 Allure 注解的测试用例
3. ✅ 生成和查看美观的测试报告
4. ✅ 使用高级功能如附件、步骤、标签等
5. ✅ 遵循最佳实践编写可维护的测试代码

开始使用提供的示例文件，逐步掌握 Allure 报告的强大功能！
```
