# Allure 报告演示 Makefile

.PHONY: help install clean test report serve demo

# 默认目标
help:
	@echo "Allure 报告演示命令:"
	@echo "  make install    - 安装依赖"
	@echo "  make clean      - 清理结果文件"
	@echo "  make test       - 运行测试"
	@echo "  make report     - 生成报告"
	@echo "  make serve      - 启动报告服务器"
	@echo "  make demo       - 运行完整演示"
	@echo "  make android    - 运行 Android 测试"
	@echo "  make ios        - 运行 iOS 测试"
	@echo "  make smoke      - 运行冒烟测试"

# 安装依赖
install:
	@echo "📦 安装依赖..."
	pip install allure-behave
	@echo "✅ 依赖安装完成"

# 清理结果文件
clean:
	@echo "🧹 清理结果文件..."
	rm -rf allure-results/*
	rm -rf allure-report/*
	@echo "✅ 清理完成"

# 运行测试
test:
	@echo "🚀 运行测试..."
	behave features/allure_demo.feature

# 生成报告
report:
	@echo "📊 生成 Allure 报告..."
	allure generate allure-results -o allure-report --clean
	@echo "✅ 报告生成完成: allure-report/index.html"

# 启动报告服务器
serve:
	@echo "🌐 启动报告服务器..."
	allure serve allure-results

# 完整演示
demo: clean test report
	@echo "🎯 演示完成！"
	@echo "📁 查看报告: open allure-report/index.html"

# Android 平台测试
android:
	@echo "🤖 运行 Android 测试..."
	PLATFORM=android behave features/allure_demo.feature

# iOS 平台测试
ios:
	@echo "🍎 运行 iOS 测试..."
	PLATFORM=ios behave features/allure_demo.feature

# 冒烟测试
smoke:
	@echo "💨 运行冒烟测试..."
	behave --tags=@smoke features/allure_demo.feature

# 回归测试
regression:
	@echo "🔄 运行回归测试..."
	behave --tags=@regression features/allure_demo.feature

# 检查环境
check:
	@echo "🔍 检查环境..."
	@python -c "import allure_behave; print('✅ allure-behave 已安装')" || echo "❌ allure-behave 未安装"
	@allure --version || echo "❌ allure 命令行工具未安装"
