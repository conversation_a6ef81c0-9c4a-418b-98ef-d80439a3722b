#!/usr/bin/env python3
"""
Allure 报告演示运行脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def setup_environment():
    """设置测试环境"""
    # 确保必要的目录存在
    try:
        os.makedirs("allure-results", exist_ok=True)
        os.makedirs("allure-report", exist_ok=True)
    except FileExistsError:
        # 如果是文件而不是目录，先删除再创建
        if os.path.isfile("allure-results"):
            os.remove("allure-results")
            os.makedirs("allure-results")
        if os.path.isfile("allure-report"):
            os.remove("allure-report")
            os.makedirs("allure-report")

    # 清理之前的结果
    if os.path.exists("allure-results") and os.path.isdir("allure-results"):
        for file in os.listdir("allure-results"):
            if file.endswith(('.json', '.txt', '.properties')):
                file_path = os.path.join("allure-results", file)
                if os.path.isfile(file_path):
                    os.remove(file_path)


def run_behave_tests(feature_file=None, platform="android", tags=None):
    """运行 behave 测试"""
    print(f"🚀 开始运行测试 - 平台: {platform}")

    # 设置环境变量
    env = os.environ.copy()
    env['PLATFORM'] = platform

    # 构建 behave 命令
    cmd = ["behave"]

    # 添加配置参数
    cmd.extend([
        "--format", "allure_behave.formatter:AllureFormatter",
        "--outdir", "allure-results",
        "--no-capture",
        "--no-color"
    ])

    # 添加标签过滤
    if tags:
        cmd.extend(["--tags", tags])

    # 添加特定的 feature 文件
    if feature_file:
        cmd.append(f"features/{feature_file}")
    else:
        cmd.append("features/")

    print(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 测试执行成功")
        else:
            print("❌ 测试执行失败")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)

        return result.returncode
    except Exception as e:
        print(f"❌ 执行测试时发生错误: {e}")
        return 1


def generate_allure_report():
    """生成 Allure 报告"""
    print("📊 生成 Allure 报告...")

    try:
        # 检查 allure 命令是否可用
        subprocess.run(["allure", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Allure 命令行工具未安装")
        print("请安装 Allure: brew install allure (macOS) 或访问 https://docs.qameta.io/allure/")
        return False

    try:
        # 生成报告
        cmd = ["allure", "generate", "allure-results", "-o", "allure-report", "--clean"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Allure 报告生成成功")
            print("📁 报告位置: allure-report/index.html")
            return True
        else:
            print("❌ 生成报告失败")
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"❌ 生成报告时发生错误: {e}")
        return False


def serve_allure_report():
    """启动 Allure 报告服务器"""
    print("🌐 启动 Allure 报告服务器...")

    try:
        # 启动服务器
        subprocess.run(["allure", "serve", "allure-results"])
    except KeyboardInterrupt:
        print("\n👋 报告服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器时发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Allure 报告演示")
    parser.add_argument(
        "-f", "--feature",
        help="指定要运行的 feature 文件 (例如: allure_demo.feature)"
    )
    parser.add_argument(
        "-p", "--platform",
        choices=["android", "ios"],
        default="android",
        help="指定测试平台"
    )
    parser.add_argument(
        "-t", "--tags",
        help="指定要运行的标签 (例如: @smoke)"
    )
    parser.add_argument(
        "--no-report",
        action="store_true",
        help="只运行测试，不生成报告"
    )
    parser.add_argument(
        "--serve",
        action="store_true",
        help="生成报告后启动服务器"
    )

    args = parser.parse_args()

    print("🎯 Allure 报告演示")
    print("=" * 50)

    # 设置环境
    setup_environment()

    # 运行测试
    exit_code = run_behave_tests(
        feature_file=args.feature,
        platform=args.platform,
        tags=args.tags
    )

    if exit_code != 0:
        print("❌ 测试失败，但仍会生成报告")

    # 生成报告
    if not args.no_report:
        report_generated = generate_allure_report()

        if report_generated and args.serve:
            serve_allure_report()

    print("\n📋 使用说明:")
    print("1. 查看报告: open allure-report/index.html")
    print("2. 启动服务器: allure serve allure-results")
    print("3. 重新生成: allure generate allure-results -o allure-report --clean")


if __name__ == "__main__":
    main()
