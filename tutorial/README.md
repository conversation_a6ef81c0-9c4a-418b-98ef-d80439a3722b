# 🎯 Behave-Allure 报告教程

这是一个完整的 behave-allure 测试报告教程，包含实际可运行的示例代码。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 Python 依赖
pip install allure-behave

# 安装 Allure 命令行工具 (macOS)
brew install allure

# 或者使用 Makefile
make install
```

### 2. 运行演示

```bash
# 方式一：使用 Python 脚本
python tutorial/run_allure_demo.py --serve

# 方式二：使用 Makefile
make demo

# 方式三：手动运行
behave features/allure_demo.feature
allure generate allure-results -o allure-report --clean
allure serve allure-results
```

### 3. 查看报告

运行完成后，Allure 报告会自动在浏览器中打开，或者手动打开：
```bash
open allure-report/index.html
```

## 📁 文件结构

```
tutorial/
├── README.md                    # 本文件
├── allure_tutorial.md          # 详细教程文档
├── run_allure_demo.py          # 演示运行脚本
├── enhanced_environment.py     # 增强的环境配置
├── Makefile                    # 便捷命令
├── features/
│   └── allure_demo.feature     # 演示 Feature 文件
├── steps/
│   └── allure_demo_steps.py    # 演示 Steps 文件
└── allure-results/
    └── environment.properties  # 环境配置
```

## 🎨 功能特性

### ✅ 已实现的功能

- **基础配置**: behave.ini 配置文件
- **测试用例**: 完整的 BDD 测试场景
- **Allure 注解**: 
  - `@allure.feature` - 功能模块
  - `@allure.story` - 用户故事  
  - `@allure.severity` - 严重程度
  - `@allure.step` - 测试步骤
- **附件支持**: 文本、JSON、图片附件
- **环境信息**: 动态环境配置
- **标签过滤**: 支持 @smoke、@regression 等标签
- **多平台**: 支持 Android/iOS 平台切换
- **失败处理**: 失败时自动添加详细信息

### 📊 报告内容

生成的 Allure 报告包含：

1. **Overview** - 测试执行概览
2. **Categories** - 失败分类统计
3. **Suites** - 测试套件详情
4. **Graphs** - 图表统计
5. **Timeline** - 执行时间线
6. **Behaviors** - BDD 行为视图
7. **Packages** - 包结构视图

## 🛠️ 使用方法

### 基本命令

```bash
# 运行所有测试
behave

# 运行特定 feature
behave features/allure_demo.feature

# 按标签运行
behave --tags=@smoke

# 指定平台
PLATFORM=android behave features/allure_demo.feature
```

### 使用 Makefile

```bash
make help        # 查看所有命令
make test        # 运行测试
make report      # 生成报告
make serve       # 启动服务器
make android     # Android 测试
make ios         # iOS 测试
make smoke       # 冒烟测试
make clean       # 清理文件
```

### 使用 Python 脚本

```bash
# 基本运行
python tutorial/run_allure_demo.py

# 指定参数
python tutorial/run_allure_demo.py -f allure_demo.feature -p android --serve

# 查看帮助
python tutorial/run_allure_demo.py --help
```

## 📚 学习路径

1. **阅读教程**: 查看 `allure_tutorial.md` 了解详细概念
2. **运行示例**: 执行 `make demo` 查看实际效果
3. **查看代码**: 研究 `features/` 和 `steps/` 中的示例代码
4. **自定义测试**: 基于示例创建自己的测试用例
5. **集成项目**: 将学到的知识应用到实际项目中

## 🔧 自定义配置

### 修改测试数据

编辑 `features/allure_demo.feature` 添加新的测试场景：

```gherkin
@allure.story:新功能测试
@allure.severity:normal
Scenario: 我的新测试场景
  Given 我的前置条件
  When 我执行某个操作
  Then 我验证某个结果
```

### 添加新的步骤

在 `steps/allure_demo_steps.py` 中添加对应的步骤实现：

```python
@allure.step("我的新步骤")
@given('我的前置条件')
def my_new_step(context):
    # 实现逻辑
    pass
```

### 环境配置

修改 `allure-results/environment.properties` 添加环境信息：

```properties
Custom.Property=Custom.Value
Test.Environment=Development
```

## 🐛 常见问题

### Q: Allure 命令找不到
A: 确保已安装 Allure 命令行工具：`brew install allure`

### Q: 中文显示乱码
A: 在 steps 文件开头添加编码声明：`# -*- coding: utf-8 -*-`

### Q: 报告加载慢
A: 检查附件大小，避免添加过大的附件

### Q: 步骤不显示
A: 确保使用了 `@allure.step` 装饰器

## 📞 获取帮助

- 查看详细教程：`tutorial/allure_tutorial.md`
- 运行示例代码：`python tutorial/run_allure_demo.py --help`
- 官方文档：https://docs.qameta.io/allure/

---

🎉 **开始你的 Allure 报告之旅吧！**
