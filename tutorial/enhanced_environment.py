import os
import allure
import json
from datetime import datetime
from behave import fixture


def before_all(context):
    """在所有测试开始前执行"""
    print("Before all tests")
    
    # 设置 Allure 环境信息
    allure.dynamic.feature("自动化测试套件")
    
    # 获取环境变量
    platform = os.environ.get('PLATFORM', 'android')
    context.platform = platform
    
    # 记录测试开始时间
    context.test_start_time = datetime.now()
    
    # 添加环境信息到 Allure 报告
    with allure.step("初始化测试环境"):
        env_info = {
            "platform": platform,
            "test_start_time": context.test_start_time.isoformat(),
            "python_version": os.sys.version,
            "working_directory": os.getcwd()
        }
        
        allure.attach(
            json.dumps(env_info, indent=2),
            name="环境信息",
            attachment_type=allure.attachment_type.JSON
        )


def before_feature(context, feature):
    """在每个 Feature 开始前执行"""
    print(f"Before feature: {feature.name}")
    
    # 设置 Feature 级别的 Allure 信息
    allure.dynamic.feature(feature.name)
    
    # 从 feature 标签中提取信息
    if hasattr(feature, 'tags'):
        for tag in feature.tags:
            if tag.startswith('allure.feature'):
                feature_name = tag.split(':')[1] if ':' in tag else tag
                allure.dynamic.feature(feature_name)


def before_scenario(context, scenario):
    """在每个 Scenario 开始前执行"""
    print(f"Before scenario: {scenario.name}")
    
    # 设置 Scenario 级别的 Allure 信息
    allure.dynamic.title(scenario.name)
    
    # 处理标签
    if hasattr(scenario, 'tags'):
        for tag in scenario.tags:
            if tag.startswith('allure.story'):
                story_name = tag.split(':')[1] if ':' in tag else tag
                allure.dynamic.story(story_name)
            elif tag.startswith('allure.severity'):
                severity = tag.split(':')[1] if ':' in tag else tag
                allure.dynamic.severity(severity)
            elif tag in ['smoke', 'regression', 'functional', 'negative']:
                allure.dynamic.tag(tag)
    
    # 记录 scenario 开始时间
    context.scenario_start_time = datetime.now()


def before_step(context, step):
    """在每个 Step 开始前执行"""
    # 可以在这里添加步骤级别的日志
    pass


def after_step(context, step):
    """在每个 Step 结束后执行"""
    # 如果步骤失败，添加失败信息
    if step.status == "failed":
        with allure.step(f"步骤失败: {step.name}"):
            allure.attach(
                f"失败的步骤: {step.name}\n错误信息: {step.exception}",
                name="步骤失败详情",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 如果有截图功能，可以在这里添加截图
            # screenshot_path = take_screenshot()
            # if screenshot_path:
            #     allure.attach.file(
            #         screenshot_path,
            #         name="失败截图",
            #         attachment_type=allure.attachment_type.PNG
            #     )


def after_scenario(context, scenario):
    """在每个 Scenario 结束后执行"""
    print(f"After scenario: {scenario.name}")
    
    # 计算执行时间
    if hasattr(context, 'scenario_start_time'):
        execution_time = datetime.now() - context.scenario_start_time
        
        allure.attach(
            f"执行时间: {execution_time.total_seconds():.2f} 秒",
            name="执行时间统计",
            attachment_type=allure.attachment_type.TEXT
        )
    
    # 根据场景结果添加不同的信息
    if scenario.status == "failed":
        allure.attach(
            f"场景失败: {scenario.name}",
            name="失败场景信息",
            attachment_type=allure.attachment_type.TEXT
        )
    elif scenario.status == "passed":
        allure.attach(
            f"场景通过: {scenario.name}",
            name="成功场景信息",
            attachment_type=allure.attachment_type.TEXT
        )


def after_feature(context, feature):
    """在每个 Feature 结束后执行"""
    print(f"After feature: {feature.name}")
    
    # 可以在这里添加 Feature 级别的清理工作
    pass


def after_all(context):
    """在所有测试结束后执行"""
    print("After all tests")
    
    # 计算总执行时间
    if hasattr(context, 'test_start_time'):
        total_execution_time = datetime.now() - context.test_start_time
        
        summary_info = {
            "total_execution_time": f"{total_execution_time.total_seconds():.2f} 秒",
            "test_end_time": datetime.now().isoformat(),
            "platform": context.platform
        }
        
        allure.attach(
            json.dumps(summary_info, indent=2),
            name="测试执行总结",
            attachment_type=allure.attachment_type.JSON
        )
