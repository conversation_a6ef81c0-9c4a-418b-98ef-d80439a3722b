import os


class DefaultVTData:
    aid = 680122939
    cid = 10258390
    epid = 137187
    sid = 35914
    epid_status = 2
    type = 4
    sub_type = 1
    auto_play = 0
    spmid = "united.player-video-detail.0.0"


class DefaultAndroidVTData:
    from_spmid = "tm.recommend.0.0"
    extra = {
        "from_outer_spmid": "tm.recommend.0.0",
        "inline_type": 1
    }
    track_id = "59150f15-3bf6-4448-8eb3-e92e2d7df2e14"


class DefaultIOSVTData:
    from_spmid = "tm.recommend.0.0|tm.recommend.0.0"
    extra = {
        "inline_type": '1'
    }


class VTSettingData:
    log_id = "000891"

    # 不同平台的buvid配置
    _buvid_config = {
        "android": "XUAF186A0E8ECFBC0FF8A21CAE219DA179878",
        "ios": "YF456DF5968C2D6B42D0806F445C1C3E090A"
    }

    @classmethod
    def get_buvid(cls, platform=None):
        """
        根据平台获取对应的buvid

        Args:
            platform: 平台名称 ('android' 或 'ios')，如果为None则从环境变量获取

        Returns:
            str: 对应平台的buvid
        """
        if platform is None:
            platform = os.getenv('PLATFORM').lower()

        return cls._buvid_config.get(platform, cls._buvid_config['android'])

    # 类属性方式访问（兼容性）
    @classmethod
    def get_current_buvid(cls):
        """获取当前环境的buvid"""
        return cls.get_buvid()


class QualityData:
    quality_360p = 16
    quality_480p = 32
    quality_720p = 64
    quality_1080p = 80
    quality_1080p_high = 112
    quality_1080p_60fps = 116
    quality_4k = 120


class BuryingPointUserData:
    mid = "7068152479044402"


class DefaultSwitchEPData:
    """默认的切集埋点数据, 切第三集"""
    aid = 110812916091904
    cid = 500000700483775
    epid = 145434
    from_spmid = "united.player-video-detail.episode.3"
