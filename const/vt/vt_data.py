import copy
import os
from abc import ABC, abstractmethod

from const.default_data import DefaultVTData, VTSettingData, DefaultIOSVTData, DefaultAndroidVTData


class BaseVTExpectedData(ABC):
    """VT期望数据基类"""

    def __init__(self, played_time=0, paused_time=0, total_time=0, actual_played_time=0,
                 last_play_progress_time=0, max_play_progress_time=0, miniplayer_play_time=0):

        self.data = {
            **self._init_device_data(),
            **self._init_default_vt_data(),
            **self._init_play_time_data(played_time, paused_time, total_time, actual_played_time,
                                       last_play_progress_time, max_play_progress_time, miniplayer_play_time)
        }

    def _init_default_vt_data(self):
        """初始化默认VT数据"""
        return {
            "aid": DefaultVTData.aid,
            "cid": DefaultVTData.cid,
            "epid": DefaultVTData.epid,
            "sid": DefaultVTData.sid,
            "epid_status": DefaultVTData.epid_status,
            "type": DefaultVTData.type,
            "sub_type": DefaultVTData.sub_type,
            "auto_play": DefaultVTData.auto_play,
            "spmid": DefaultVTData.spmid,
        }

    def _init_play_time_data(self, played_time, paused_time, total_time, actual_played_time,
                            last_play_progress_time, max_play_progress_time, miniplayer_play_time):
        """初始化播放时间相关数据"""
        return {
            "played_time": played_time,
            "paused_time": paused_time,
            "total_time": total_time,
            "actual_played_time": actual_played_time,
            "last_play_progress_time": last_play_progress_time,
            "max_play_progress_time": max_play_progress_time,
            "miniplayer_play_time": miniplayer_play_time
        }

    @abstractmethod
    def _init_device_data(self):
        """初始化设备相关数据 - 子类必须实现"""
        pass

    @abstractmethod
    def get_platform_name(self):
        """获取平台名称 - 子类必须实现"""
        pass

    def update_expect_play_time_data(self, played_time=None, paused_time=None, total_time=None, actual_played_time=None,
                                     last_play_progress_time=None, max_play_progress_time=None,
                                     miniplayer_play_time=None):
        """更新播放时间相关数据"""
        updates_arg = {
            k: v for k, v in locals().items()
            if k != 'self' and v is not None
        }
        self.data.update(updates_arg)

    def update_vt_expect_data(self, **kwargs):
        """更新VT期望数据"""
        self.data.update(kwargs)

    def get_vt_expect_data(self):
        """获取VT期望数据的深拷贝"""
        return copy.deepcopy(self.data)

    def set_vt_expect_data(self, data):
        """设置VT期望数据"""
        self.data = data


class AndroidVTExpectedData(BaseVTExpectedData):
    """Android平台VT期望数据"""

    def _init_device_data(self):
        """初始化Android设备相关数据"""
        return {
            "mobi_app": "android",
            "device": "",
            "buvid": VTSettingData.get_buvid("android"),
            # 安卓和 IOS 不一致字段
            "from_spmid": DefaultAndroidVTData.from_spmid,
            "extra": copy.deepcopy(DefaultAndroidVTData.extra), # 深拷贝避免共享引用
        }

    def get_platform_name(self):
        return "android"


class IOSVTExpectedData(BaseVTExpectedData):
    """iOS平台VT期望数据"""

    def _init_device_data(self):
        """初始化iOS设备相关数据"""
        return {
            "mobi_app": "iphone",
            "device": "phone",
            "buvid": VTSettingData.get_buvid("ios"),
            # 安卓和 IOS 不一致字段
            "from_spmid": DefaultIOSVTData.from_spmid,
            "extra": copy.deepcopy(DefaultIOSVTData.extra),  # 深拷贝避免共享引用
        }

    def get_platform_name(self):
        return "ios"




class VTExpectedData:
    """VT期望数据工厂类 - 保持向后兼容性"""

    # 平台映射
    _platform_classes = {
        "android": AndroidVTExpectedData,
        "ios": IOSVTExpectedData
    }

    def __new__(cls, platform=None, *args, **kwargs):
        """工厂方法：根据平台创建对应的实例"""

        platform = platform or os.getenv('PLATFORM').lower()

        if platform not in cls._platform_classes:
            raise ValueError(f"不支持的平台: {platform}，支持的平台: {list(cls._platform_classes.keys())}")

        # 创建对应平台的实例
        platform_class = cls._platform_classes[platform]
        return platform_class(*args, **kwargs)

    @classmethod
    def create_android(cls, *args, **kwargs):
        """直接创建Android VT数据实例"""
        return AndroidVTExpectedData(*args, **kwargs)

    @classmethod
    def create_ios(cls, *args, **kwargs):
        """直接创建iOS VT数据实例"""
        return IOSVTExpectedData(*args, **kwargs)

    @classmethod
    def get_supported_platforms(cls):
        """获取支持的平台列表"""
        return list(cls._platform_classes.keys())


if __name__ == "__main__":
    android_vt = VTExpectedData("android", played_time=30)
    ios_vt = VTExpectedData("ios", played_time=30)
    print(android_vt.get_vt_expect_data())
    print(ios_vt.get_vt_expect_data())
