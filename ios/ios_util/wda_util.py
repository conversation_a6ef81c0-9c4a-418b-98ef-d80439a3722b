import tidevice
import wda

from lib.lib_img import get_image
from lib.lib_location import LibLocation
import configparser

def connect_ios_device() -> wda.USBClient:
    device = tidevice.Device().device_info()
    uuid = (device.get("UniqueDeviceID"))
    try:
        wda_client = wda.USBClient(uuid, wda_bundle_id="bilibili.ep.eptest.WebDriverAgentRunner.xctrunner")
        print(wda_client.device_info())
        wda_client.wait_ready(timeout=300, noprint=True)
        print("WDA 连接")
    except Exception as e:
        print("WDA 连接错误：", e)
        return None
    return wda_client

def wda_image_location(platform, img1_name, img2_name):
    # Convert to wda coordinates
    c = connect_ios_device()
    img1 = get_image(platform,img1_name)
    img2 = get_image(platform,img2_name)
    x, y, w, h = LibLocation.get_img_location(img1, img2)
    screen_size = c.session().window_size()
    screen_width = screen_size.width
    screen_height = screen_size.height
    # Convert to wda coordinates
    scale_x = screen_width / img2.shape[1]
    scale_y = screen_height / img2.shape[0]
    wda_x = int(x * scale_x)
    wda_y = int(y * scale_y)
    wda_w = int(w * scale_x)
    wda_h = int(h * scale_y)
    print(wda_x, wda_y, wda_w, wda_h)
    return wda_x, wda_y, wda_w, wda_h


def wda_text_location(platform, target_text, img):
    # Convert to wda coordinates
    c = connect_ios_device()
    img = get_image(platform,img)
    center_x, center_y = LibLocation.get_text_location(target_text, img)
    # Get the screen size
    screen_size = c.session().window_size()
    screen_width = screen_size.width
    screen_height = screen_size.height

    # Convert to wda coordinates
    scale_x = screen_width / img.shape[1]
    scale_y = screen_height / img.shape[0]
    wda_center_x = int(center_x * scale_x)
    wda_center_y = int(center_y * scale_y)
    print(wda_center_x, wda_center_y)
    return wda_center_x, wda_center_y



if __name__ == '__main__':
    d = connect_ios_device()
    config = configparser.ConfigParser()
    config.read('config.ini')
    app_bundle_id = (config.get('ios', 'app_bundle_id'))
    d.session(app_bundle_id)