import configparser
import logging
import os
import shutil
import time
import warnings
from typing import Optional, Dict, Any

import tidevice
from opt_einsum.sharing import currently_sharing
from wda import USBClient

from config.global_config import AndroidConfig, IOSConfig
from ios.ios_util.wda_util import connect_ios_device
from android.android_util.u2_util import connect_android_device
import uiautomator2 as u2
from abc import ABC, abstractmethod
from typing import Tuple, List, Callable

from cross_ops.locate_data_class import LocatorData, ImageRecognition, TextRecognition, LocatorFactory
from abc import ABC, abstractmethod
from typing import Dict, Type, Optional, Tuple
from pydantic import BaseModel

from cross_ops.picture_component import PictureService
from loguru import logger

from utils.ai_execute_util import AiExecuteUtil

temp_pic_path = os.path.join("temp_pic")
actual_image_path = os.path.join(temp_pic_path)
template_image_path = os.path.join("ppocr_img", "android")
currently_screenshot_file_name = "screenshot.png"


class ActionRequest(BaseModel):
    """标准化操作请求（矩形坐标版）"""
    rect: Tuple[float, float, float, float]  # (x1, y1, x2, y2) 左上到右下
    meta: Dict[str, Any] = None

    class Config:
        extra = "forbid"  # 禁止传递未定义的字段

    @property
    def center(self) -> Tuple[float, float]:
        """获取矩形中心点坐标"""
        x1, y1, x2, y2 = self.rect
        return (x1 + x2) / 2, (y1 + y2) / 2

    @property
    def width(self) -> float:
        """矩形宽度"""
        return self.rect[2] - self.rect[0]

    @property
    def height(self) -> float:
        """矩形高度"""
        return self.rect[3] - self.rect[1]



class BasePlatformOperation(ABC):
    """
    平台操作基类
    """

    def __init__(self):
        self.driver = None

    @abstractmethod
    def locate_element(self, locator: LocatorData) -> Optional[ActionRequest]:
        """平台级元素查找"""
        pass

    @abstractmethod
    def _get_element_locators(self, element_str: str):
        pass

    @abstractmethod
    def screenshot(self, image_name: str):
        pass

    @abstractmethod
    def start_app(self):
        pass

    @abstractmethod
    def set_proxy(self, ip: str, port: str):
        pass

    @abstractmethod
    def close_proxy(self):
        pass

    @abstractmethod
    def get_serial(self):
        pass

    def _get_element(self, element_str: str):
        for locator_func in self._get_element_locators(element_str):
            element = locator_func()
            if element.exists:
                return element
        return None

    def click(self, locator: LocatorData) -> None:
        """带自动补全的点击操作"""
        if req := self._resolve_locator(locator):
            center_x, center_y = req.center
            self.driver.click(center_x, center_y)

    def send_keys(self, locator: LocatorData, text: str) -> None:
        """带自动补全的输入操作"""
        if req := self._resolve_locator(locator):
            center_x, center_y = req.center
            self.driver.click(center_x, center_y)  # 先点击
            self.driver.send_keys(text)

    def _resolve_locator(self, locator: LocatorData) -> Optional[ActionRequest]:
        """统一解析定位器"""
        try:
            return self.locate_element(locator)
        except (ElementNotFoundException, LocatorStringNotFoundException) as e:
            # 回退到通用定位
            self.screenshot(currently_screenshot_file_name)
            return OperationCenter.generic_locate(locator)
        except Exception as e:
            logger.error("发生未知错误, {}", e)
            raise e


class GenericLocator:
    """通用定位器（无状态服务）"""

    @classmethod
    def text_locate(cls, text: str, image_path: str, confidence: float) -> ActionRequest:
        """文字识别定位入口"""
        picture_service = PictureService()
        position = picture_service.get_text_position(text, image_path)
        return ActionRequest(rect=(position[0], position[1], position[2], position[3]))

    @classmethod
    def image_locate(cls, template_path: str, image_path: str, confidence: float) -> ActionRequest:
        """
        图像识别定位入口, 暂未实现
        """
        picture_service = PictureService()
        position = picture_service.get_image_position(template_path, image_path)
        return ActionRequest(rect=(position[0], position[1], position[2], position[3]))


# 具体平台实现
class AndroidOperation(BasePlatformOperation):

    def __init__(self):
        super().__init__()
        self.driver = connect_android_device()

    def get_serial(self):
        serial = self.driver.device_info['serial']
        logger.info(f"获取到设备 ID, serial: {serial}")
        return serial

    def set_proxy(self, ip: str, port: str):
        """设置代理"""
        try:
            mock_server = ip + ":" + port
            cmd_res = os.system(f"adb shell settings put global http_proxy {mock_server}")
            if cmd_res != 0:
                logger.error("设置手机代理出错, cmd_res: {}".format(cmd_res))
                return False
        except Exception as e:
            logger.error("设置手机代理出错: {}".format(e))
        logger.info("设置手机代理成功")
        return True

    def close_proxy(self):
        commands = [
            "adb shell settings delete global http_proxy ",
            "adb shell settings delete global global_http_proxy_host ",
            "adb shell settings delete global global_http_proxy_port",
            "adb shell settings delete global global_https_proxy_host",
            "adb shell settings delete global global_https_proxy_port",
            "adb shell settings delete global https_proxy",
            "adb shell settings put global http_proxy :0"
        ]
        for command in commands:
            try:
                result = os.system(command)
                logger.info(f"Command '{command}' executed successfully:{result}")
            except Exception as e:
                logger.error("设置手机代理出错, 异常信息: {}".format(e))

    def start_app(self):
        logger.info("启动安卓app")
        try:
            self.driver.app_start(AndroidConfig.app_package_name, stop=True)
            logger.info(f"成功启动安卓应用: {AndroidConfig.app_package_name}")
        except Exception as e:
            logger.error(f"启动安卓应用失败: {e}")
            raise

    def locate_element(self, locator: LocatorData) -> ActionRequest:
        """安卓原生定位实现"""
        locator_strings = locator.locator_strings.get("android")
        if locator_strings:
            for item in locator_strings:
                element: u2.UiObject = self._get_element(item)
                if element:
                    element_bounds = element.info["bounds"]
                    windows_size = self.driver.window_size()
                    left = element_bounds["left"] / windows_size[0]
                    top = element_bounds["top"] / windows_size[1]
                    right = element_bounds["right"] / windows_size[0]
                    bottom = element_bounds["bottom"] / windows_size[1]
                    return ActionRequest(rect=(left, top, right, bottom))

            # 所有字符串都遍历完, 还没返回, 抛异常
            raise ElementNotFoundException("元素未找到")
        else:
            raise LocatorStringNotFoundException("未找到安卓定位字符串")

    def _get_element_locators(self, element_str: str):
        return (
            lambda: self.driver.xpath(element_str),
            lambda: self.driver(description=element_str),
            lambda: self.driver(resourceId=element_str),
            lambda: self.driver(text=element_str)
        )


    def screenshot(self, image_name: str):
        file_path = os.path.join(actual_image_path, image_name)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        self.driver.screenshot(file_path)



class IOSOperation(BasePlatformOperation):
    """
    IOS操作封装, 暂时未实现
    """

    def __init__(self):
        super().__init__()
        self.driver = connect_ios_device()

    def locate_element(self, locator: LocatorData) -> ActionRequest:
        """iOS原生定位实现"""
        locator_strings = locator.locator_strings.get("ios")
        if locator_strings:
            for item in locator_strings:
                element = self._get_element(item)
                if element and element.exists:
                    # 获取元素的边界信息
                    element_bounds = element.bounds
                    # 获取屏幕尺寸
                    window_size = self.driver.window_size()
                    screen_width = window_size.width
                    screen_height = window_size.height

                    # 计算归一化坐标
                    left = element_bounds.x / screen_width
                    top = element_bounds.y / screen_height
                    right = (element_bounds.x + element_bounds.width) / screen_width
                    bottom = (element_bounds.y + element_bounds.height) / screen_height

                    return ActionRequest(rect=(left, top, right, bottom))

            # 所有字符串都遍历完, 还没返回, 抛异常
            raise ElementNotFoundException("元素未找到")
        else:
            raise LocatorStringNotFoundException("未找到iOS定位字符串")

    def _get_element_locators(self, element_str: str):
        """iOS元素定位器列表"""
        return (
            lambda: self.driver(name=element_str),           # 通过name属性定位
            lambda: self.driver(label=element_str),          # 通过label属性定位
            lambda: self.driver(value=element_str),          # 通过value属性定位
            lambda: self.driver.xpath(element_str),          # 通过xpath定位
        )

    def screenshot(self, image_name: str):
        """iOS截图实现"""
        file_path = os.path.join(actual_image_path, image_name)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        # 使用wda的截图功能
        screenshot_data = self.driver.screenshot()
        with open(file_path, 'wb') as f:
            f.write(screenshot_data)
        logger.info(f"iOS截图保存到: {file_path}")

    def start_app(self):
        """启动iOS应用"""
        logger.info("启动iOS app")
        try:
            self.driver.session(IOSConfig.app_bundle_id)
            logger.info(f"成功启动iOS应用: {IOSConfig.app_bundle_id}")
        except Exception as e:
            logger.error(f"启动iOS应用失败: {e}")
            raise

    def set_proxy(self, ip: str, port: str):
        self._goto_proxy_setting()
        self.driver(label="手动").click()
        self.driver.sleep(1)
        self.driver(label="服务器").clear_text()
        self.driver(label="服务器").set_text(ip)
        self.driver.sleep(1)
        self.driver(label="端口").clear_text()
        self.driver(label="端口").set_text(str(port))
        self.driver.sleep(1)
        self.driver(label="存储").click()
        self.driver.sleep(1)

    def close_proxy(self):
        self._goto_proxy_setting()
        self.driver(label="关闭").click()
        self.driver.sleep(1)
        self.driver(label="存储").click()
        self.driver.sleep(1)

    def get_serial(self):
        # 创建Device实例
        device = tidevice.Device()
        # 获取设备信息
        device_info = device.device_info()
        # 获取UDID
        udid = device_info.get("UniqueDeviceID")
        logger.info(f"获取到设备 ID, udid: {udid}")
        return udid


    def _goto_proxy_setting(self):
        self.driver.session(bundle_id="com.apple.Preferences")
        self.driver.sleep(1)
        self.driver(label="无线局域网").click()
        self.driver.sleep(1)
        self.driver(label="已选择").click()
        self.driver.sleep(1)
        self.driver.swipe(0.5, 0.8, 0.5, 0.2)
        self.driver(label="配置代理").click()
        self.driver.sleep(1)


class OperationCenter:
    """操作中心"""
    _platform_ops: Dict[str, Type[BasePlatformOperation]] = {
        "android": AndroidOperation,
        "ios": IOSOperation,
    }

    @classmethod
    def get_operator(cls) -> BasePlatformOperation:
        """获取当前平台操作器"""
        platform = os.getenv("PLATFORM")
        if platform is None:
            raise EnvironmentError("PLATFORM环境变量未设置, 请设置为android或ios")
        return cls._platform_ops[platform.lower()]()

    @classmethod
    def generic_locate(cls, locator: LocatorData) -> ActionRequest:
        """通用定位兜底"""
        # 获取文本定位数据
        text_recognitions: List[TextRecognition] = locator.get_recognition_by_type(TextRecognition)
        for text_recognition in text_recognitions:
            # 实现文字识别定位逻辑
            if text_recognition.text_content:
                action_request = GenericLocator.text_locate(
                    text_recognition.text_content,
                    os.path.join(actual_image_path, currently_screenshot_file_name),
                    text_recognition.confidence
                )
                logger.info(f"文本定位成功: action_request:  {action_request}")
                # 删除临时图片文件夹
                logger.info("删除临时图片文件夹")
                shutil.rmtree(os.path.join(temp_pic_path))
                return action_request
        # 获取图片定位数据
        image_recognitions: List[ImageRecognition] = locator.get_recognition_by_type(ImageRecognition)
        for image_recognition in image_recognitions:
            # 实现图片识别定位逻辑
            if image_recognition.template_path:
                action_request = GenericLocator.image_locate(
                    os.path.join(template_image_path, image_recognition.template_path),
                    os.path.join(actual_image_path, currently_screenshot_file_name),
                    image_recognition.confidence
                )
                logger.info(f"图片定位成功: action_request:  {action_request}")
                # 删除临时图片文件夹
                logger.info("删除临时图片文件夹")
                shutil.rmtree(os.path.join(temp_pic_path))
                return action_request


class LocatorStringNotFoundException(Exception):
    """定位字符串未找到异常"""
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


class ElementNotFoundException(Exception):
    """元素未找到异常"""
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


if __name__ == "__main__":
    os.environ["PLATFORM"] = "ios"
    PLATFORM = os.getenv("PLATFORM")
    operator = OperationCenter.get_operator()

    ai_util = AiExecuteUtil(PLATFORM, operator.get_serial())

    operator.start_app()
    ai_util.execute_testcase_by_desc("进入视频详情页")